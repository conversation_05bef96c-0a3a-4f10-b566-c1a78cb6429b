import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Check, BookOpen } from 'lucide-react';
import AssessmentQuestion from './AssessmentQuestion';
import AssessmentSidebar from './AssessmentSidebar';

const AssessmentForm = ({
  assessmentData,
  onSubmit,
  onNext,
  onPrevious,
  isLastAssessment = false,
  currentStep = 1,
  totalSteps = 3,
  isDebugMode = false,
}) => {
  const [answers, setAnswers] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const questionsPerPage = 5;

  // Scroll to top when page changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentPage]);

  // Auto-fill answers in debug mode
  useEffect(() => {
    if (isDebugMode && import.meta.env.DEV) {
      const autoAnswers = {};

      // Generate random answers for all questions (5-7 range for good scores)
      allQuestions.forEach((q) => {
        autoAnswers[q.questionKey] = Math.floor(Math.random() * 3) + 5; // Random 5-7
      });

      setAnswers(autoAnswers);
      setCurrentPage(Math.max(0, Math.ceil(allQuestions.length / questionsPerPage) - 1)); // Go to last page
    }
  }, [isDebugMode, assessmentData.title]);
  
  // Flatten all questions from all categories
  const allQuestions = [];
  Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
    // Regular questions
    category.questions.forEach((question, index) => {
      allQuestions.push({
        question,
        categoryKey,
        questionKey: `${categoryKey}_${index}`,
        isReverse: false
      });
    });
    
    // Reverse questions (for Big Five)
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((question, index) => {
        allQuestions.push({
          question,
          categoryKey,
          questionKey: `${categoryKey}_reverse_${index}`,
          isReverse: true
        });
      });
    }
  });

  const totalPages = Math.ceil(allQuestions.length / questionsPerPage);
  const currentQuestions = allQuestions.slice(
    currentPage * questionsPerPage,
    (currentPage + 1) * questionsPerPage
  );

  const handleAnswerChange = (questionKey, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionKey]: value
    }));
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const calculateScores = () => {
    const scores = {};
    
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      let totalScore = 0;
      let questionCount = 0;
      
      // Regular questions
      category.questions.forEach((_, index) => {
        const questionKey = `${categoryKey}_${index}`;
        if (answers[questionKey]) {
          totalScore += answers[questionKey];
          questionCount++;
        }
      });
      
      // Reverse questions (for Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          const questionKey = `${categoryKey}_reverse_${index}`;
          if (answers[questionKey]) {
            // Reverse the score (8 - original score for 1-7 scale)
            totalScore += (8 - answers[questionKey]);
            questionCount++;
          }
        });
      }
      
      // Calculate average score (0-100 scale)
      if (questionCount > 0) {
        scores[categoryKey] = Math.round(((totalScore / questionCount) - 1) * (100 / 6)); // Convert 1-7 to 0-100
      }
    });
    
    return scores;
  };

  const handleSubmit = () => {
    const scores = calculateScores();
    console.log('🔍 DEBUG - AssessmentForm handleSubmit called');
    console.log('🔍 DEBUG - Calculated scores:', scores);
    console.log('🔍 DEBUG - Current answers:', answers);
    console.log('🔍 DEBUG - Assessment data:', assessmentData.title);
    onSubmit(scores);
  };

  const isAssessmentComplete = () => {
    return allQuestions.every(q => answers[q.questionKey] !== undefined);
  };

  // Function to find first unanswered question
  const findFirstUnansweredQuestion = () => {
    for (let i = 0; i < allQuestions.length; i++) {
      if (answers[allQuestions[i].questionKey] === undefined) {
        return Math.floor(i / questionsPerPage);
      }
    }
    return currentPage;
  };

  // Enhanced submit handler with validation
  const handleSubmitWithValidation = () => {
    if (!isAssessmentComplete()) {
      const firstUnansweredPage = findFirstUnansweredQuestion();
      setCurrentPage(firstUnansweredPage);
      // You could also show a toast or alert here
      return;
    }
    handleSubmit();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="flex max-w-7xl mx-auto">
        {/* Main Content */}
        <div className="flex-1 lg:mr-80 p-4 lg:p-8">
          {/* Desktop Header */}
          <div className="mb-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6 max-w-3xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {assessmentData.title}
                  </h1>
                  <p className="text-gray-600 text-sm mt-1">
                    Assessment {currentStep} of {totalSteps} - {assessmentData.description}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Questions */}
          <div className="max-w-3xl mx-auto">
            {currentQuestions.map((q, index) => (
              <AssessmentQuestion
                key={q.questionKey}
                question={q.question}
                questionIndex={currentPage * questionsPerPage + index}
                totalQuestions={allQuestions.length}
                scale={assessmentData.scale}
                value={answers[q.questionKey]}
                onChange={(value) => handleAnswerChange(q.questionKey, value)}
                isReverse={q.isReverse}
              />
            ))}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex max-w-3xl mx-auto mt-8 justify-between items-center">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 0}
              className="flex items-center space-x-2 px-6 py-3 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm"
            >
              <ChevronLeft className="h-5 w-5" />
              <span>Previous Page</span>
            </button>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Page {currentPage + 1} of {totalPages}
              </span>

              {/* Previous Assessment Button */}
              {currentStep > 1 && currentPage === 0 && (
                <button
                  onClick={onPrevious}
                  className="flex items-center space-x-2 px-6 py-3 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm"
                >
                  <ChevronLeft className="h-5 w-5" />
                  <span>Previous Assessment</span>
                </button>
              )}

              {/* Submit Assessment Button */}
              {currentPage === totalPages - 1 && isLastAssessment && (
                <button
                  onClick={handleSubmitWithValidation}
                  disabled={!isAssessmentComplete()}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg"
                >
                  <Check className="h-5 w-5" />
                  <span>Submit Assessment</span>
                </button>
              )}

              {/* Next Assessment Button */}
              {currentPage === totalPages - 1 && !isLastAssessment && (
                <button
                  onClick={onNext}
                  disabled={!isAssessmentComplete()}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg"
                >
                  <span>Next Assessment</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              )}

              {/* Next Page Button */}
              {currentPage < totalPages - 1 && (
                <button
                  onClick={handleNextPage}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg"
                >
                  <span>Next Page</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Desktop Sidebar */}
        <AssessmentSidebar
          assessmentData={assessmentData}
          answers={answers}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          questionsPerPage={questionsPerPage}
          currentStep={currentStep}
          totalSteps={totalSteps}
        />
      </div>
    </div>
  );
};

export default AssessmentForm;
